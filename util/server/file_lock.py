import os

from filelock import FileLock

from util.server import (
    ROOT_REPO_BASE_DIR,
    ROOT_INDEX_BASE_DIR,
    ROOT_REPO_CACHE_BASE_DIR,
    ROOT_TRAJS_BASE_DIR
)


def get_user_repo_name(repo_path: str) -> tuple[str, str]:
    repo_name = os.path.basename(repo_path)
    user_name = os.path.basename(os.path.dirname(repo_path))
    return user_name, repo_name


def get_repo_path(user_name: str, repo_name: str) -> str:
    return os.path.join(ROOT_REPO_BASE_DIR, user_name, repo_name)


def get_repo_cache_path(user_name: str, repo_name: str) -> str:
    return os.path.join(ROOT_REPO_CACHE_BASE_DIR, user_name, repo_name)


def get_trajs_path(user_name: str, repo_name: str) -> str:
    return os.path.join(ROOT_TRAJS_BASE_DIR, user_name, repo_name)


def get_repo_index_path(user_name: str, repo_name: str) -> str:
    return os.path.join(ROOT_INDEX_BASE_DIR, user_name, repo_name)


def get_repo_lock(user_name: str, repo_name: str, timeout: float = -1) -> FileLock:
    repo_cache_lock = os.path.join(ROOT_REPO_BASE_DIR, user_name, f"{repo_name}.lock")
    return FileLock(repo_cache_lock, timeout=timeout)


def get_repo_cache_lock(
    user_name: str, repo_name: str, timeout: float = -1
) -> FileLock:
    repo_cache_lock = os.path.join(
        ROOT_REPO_CACHE_BASE_DIR, user_name, f"{repo_name}.lock"
    )
    return FileLock(repo_cache_lock, timeout=timeout)


def get_repo_index_lock(
    user_name: str, repo_name: str, timeout: float = -1
) -> FileLock:
    repo_index_lock = os.path.join(ROOT_INDEX_BASE_DIR, user_name, f"{repo_name}.lock")
    return FileLock(repo_index_lock, timeout=timeout)


__all__ = [
    get_repo_path,
    get_user_repo_name,
    get_repo_cache_path,
    get_trajs_path,
    get_repo_index_path,
    get_repo_lock,
    get_repo_cache_lock,
    get_repo_index_lock,
]
