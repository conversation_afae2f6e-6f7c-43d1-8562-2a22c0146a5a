import dspy
import os
import time
import json
from datetime import datetime
from pathvalidate import sanitize_filename

from auto_search_main import Localize
from plugins.location_tools.repo_ops.index import Index
from plugins.location_tools.repo_ops.repo_ops import RepoOps

from util.server.file_lock import (
    get_repo_index_lock,
    get_repo_index_path,
    get_trajs_path
)
from util.server import logger


lm = dspy.LM(
    "openai/DeepSeek-V3",
    cache=False,
    num_retries=15,
    api_key="7d6c438d2b0441bd96c79d885fb06503",
    base_url="https://maas-apigateway.dt.zte.com.cn/STREAM/deepseek-v3-api/v1",
)
dspy.configure(lm=lm)


def retrieve_code(user_instruction: str, user_name: str, workspace: str) -> str:
    repo_ops = load_repo_ops(user_name, workspace)

    react = dspy.ReAct(
        Localize,
        max_iters=20,
        tools=[
            repo_ops.search_code_snippets,
            repo_ops.explore_tree_structure,
            repo_ops.get_entity_contents,
        ],
    )
    loc_start_time = time.time()
    pred = react(problem_statement=user_instruction)
    loc_end_time = time.time()
    loc_time = loc_end_time - loc_start_time
    try:
        traj_data = {
            'user': user_name,
            'workspace': workspace,
            'problem_statement': user_instruction,
            'time': loc_time,
            'messages': pred.trajectory
        }
        repo_name = sanitize_filename(workspace, replacement_text="_")
        traj_path = get_trajs_path(user_name, repo_name)
        current_time = datetime.now()
        time_string = current_time.strftime("%Y%m%d%H%M%S")
        loc_trajs_path = os.path.join(traj_path, time_string, "loc_trajs.json")
        os.makedirs(os.path.dirname(loc_trajs_path), exist_ok=True)
        with open(loc_trajs_path, "w", encoding="utf-8") as f:
            f.write(json.dumps(traj_data) + "\n")
    except Exception as e:
        logger.error(f"save trajs error: {e}")

    return pred


def load_repo_ops(user_name: str, workspace: str) -> RepoOps:
    repo_name = sanitize_filename(workspace, replacement_text="_")
    repo_index_path = get_repo_index_path(user_name, repo_name)

    with get_repo_index_lock(user_name, repo_name):
        repo_index = Index.get(index_dir=repo_index_path, instance_id=repo_name)
        return RepoOps(repo_index)


__all__ = [retrieve_code, load_repo_ops]
