from concurrent.futures import Thread<PERSON>oolExecutor
import threading
import time

def square(x):
    while True:
      time.sleep(5)
      print(x * x)

from concurrent.futures import ThreadPoolExecutor
import threading
import time

class DaemonThreadPoolExecutor(ThreadPoolExecutor):
    def __init__(self, max_workers=None, thread_name_prefix=''):
        # 使用自定义线程工厂创建守护线程
        def thread_factory(*args, **kwargs):
            t = threading.Thread(*args, **kwargs)
            t.daemon = True  # 设置为守护线程
            return t
        
        super().__init__(max_workers=max_workers, 
                        thread_name_prefix=thread_name_prefix, 
                        thread_factory=thread_factory)

# def task_function(task_id):
#     print(f"Task {task_id} is running on thread {threading.current_thread().name}")
#     time.sleep(2)
#     print(f"Task {task_id} is done")

# 使用示例
# if __name__ == "__main__":
#     # 创建一个守护线程池，最大 3 个线程
#     with DaemonThreadPoolExecutor(max_workers=3, thread_name_prefix='DaemonThread') as executor:
#         # 提交任务
#         futures = [executor.submit(task_function, i) for i in range(5)]
    
#     print("Main thread is exiting")



if __name__ == "__main__":
  thread_pool = DaemonThreadPoolExecutor(max_workers=4, thread_name_prefix="daemon_worker_")


  # 将现有和未来的线程设置为daemon
  # for thread in thread_pool._threads:
  #     thread.daemon = True
  # thread_pool._thread_factory = lambda: threading.Thread(daemon=True)

  for x in range(10):
        thread_pool.submit(square, x)

  # thread_pool.shutdown()
  for t in thread_pool._threads:
     print(t.daemon)


  try:
    while True:
      time.sleep(1)
  except KeyboardInterrupt:
      print("quit")
      thread_pool.shutdown(wait=False, cancel_futures=True)


