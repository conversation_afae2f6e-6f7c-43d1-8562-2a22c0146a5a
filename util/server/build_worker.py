import os
import queue
import shutil
import traceback
import threading

from concurrent.futures import Thread<PERSON>oolExecutor
from apscheduler.schedulers.background import BackgroundScheduler

from plugins.location_tools.repo_ops.index import Index

from util.server import ROOT_REPO_CACHE_BASE_DIR, logger
from util.server.file_lock import (
    get_user_repo_name,
    get_repo_lock,
    get_repo_cache_lock,
    get_repo_index_lock,
    get_repo_path,
    get_repo_index_path,
    get_repo_cache_path,
)


TIMER_INTERVAL: int = 20
TASK_QUEUE_MAX_SIZE: int = 1000
THREAD_POOL_MAX_WORKERS: int = 8

task_queue = queue.Queue(maxsize=TASK_QUEUE_MAX_SIZE)

thread_pool = ThreadPoolExecutor(
    max_workers=THREAD_POOL_MAX_WORKERS, thread_name_prefix="ConsumerThread"
)

scheduler = BackgroundScheduler()

stop_event = threading.Event()


def get_thread_name() -> str:
    return threading.current_thread().name


def produce_directory_tasks() -> None:
    logger.info(f"Thread '{get_thread_name()}', scaning directory...")

    try:
        for user_cache_dir in os.listdir(ROOT_REPO_CACHE_BASE_DIR):
            user_cache_path = os.path.join(ROOT_REPO_CACHE_BASE_DIR, user_cache_dir)
            for repo_cache_dir in os.listdir(user_cache_path):
                repo_cache_path = os.path.join(user_cache_path, repo_cache_dir)
                if os.path.isdir(repo_cache_path):
                    for _, _, file_names in os.walk(repo_cache_path):
                        if file_names:
                            repo_path = get_repo_path(user_cache_dir, repo_cache_dir)
                            with get_repo_lock(
                                user_cache_dir, repo_cache_dir, timeout=0
                            ):
                                logger.debug(f"Locking repo folder: '{repo_path}'")
                                task_queue.put(repo_path)
                            logger.debug(f"Unlocking repo folder: '{repo_path}'")
                            break
    except Exception as e:
        logger.error(f"Sacning directory error: {e}")
        logger.error(traceback.format_exc())


def repo_copy(repo_path: str) -> None:
    user_name, repo_name = get_user_repo_name(repo_path)
    repo_cache_path = get_repo_cache_path(user_name, repo_name)

    with get_repo_cache_lock(user_name, repo_name, timeout=0):
        logger.debug(f"Locking repo cache folder: '{repo_cache_path}'")
        move_folder(repo_cache_path, os.path.dirname(repo_path))
    logger.debug(f"Unlocking repo cache folder: '{repo_cache_path}'")


def consume_directory_task() -> None:
    logger.info(f"Thread '{get_thread_name()}', waiting for tasks...")

    while not stop_event.is_set():
        try:
            repo_path = task_queue.get(timeout=1)
            logger.info(f"Get the task from the queue: '{repo_path}'")
            process_directory_task(repo_path)
        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"Error in handling the task: {e}")
            logger.error(traceback.format_exc())
        stop_event.wait(0.1)

    logger.info(f"Shutdown '{get_thread_name()}' thread...")


def process_directory_task(repo_path: str) -> None:
    logger.info(f"Thread '{get_thread_name()}', handling directory '{repo_path}'...")

    user_name, repo_name = get_user_repo_name(repo_path)
    repo_index_path = get_repo_index_path(user_name, repo_name)

    with get_repo_index_lock(user_name, repo_name, timeout=0):
        logger.debug(f"Locking index folder: '{repo_index_path}'")

        with get_repo_lock(user_name, repo_name):
            logger.debug(f"Locking repo folder: '{repo_path}'")

            # repo_cache -> repo
            repo_copy(repo_path)
            if os.listdir(repo_path):
                Index.build(
                    repo_dir=repo_path,
                    index_dir=repo_index_path,
                    instance_id=repo_name,
                )

            shutil.rmtree(repo_path)
            logger.debug(f"Deleting folder: '{repo_path}'")
        logger.debug(f"Unlocking repo folder: '{repo_path}'")
    logger.debug(f"Unlocking index folder: '{repo_index_path}'")


def move_folder(src_dir: str, dst_dir: str) -> None:
    """
    将 src_dir 中的所有内容剪切到 dst_dir
    """
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir, exist_ok=True)
    shutil.move(src_dir, dst_dir)


def produce_scheduler_start() -> None:
    scheduler.add_job(
        produce_directory_tasks,
        "interval",
        seconds=TIMER_INTERVAL,
        max_instances=1,
        name="ProducerThread",
    )
    scheduler.start()
    logger.info(f"Startup '{scheduler._thread.name}' thread...")


def consume_thread_pool_start() -> None:
    logger.info(f"Startup '{thread_pool._thread_name_prefix}' thread pool...")
    for _ in range(THREAD_POOL_MAX_WORKERS):
        thread_pool.submit(consume_directory_task)


def produce_scheduler_start_shutdown() -> None:
    logger.info(f"Shutdown '{scheduler._thread.name}' thread...")
    scheduler.pause()
    stop_event.set()
    scheduler.shutdown(wait=True)


def consume_thread_pool_start_shutdown() -> None:
    logger.info(f"Shutdown '{thread_pool._thread_name_prefix}' thread...")
    stop_event.set()
    thread_pool.shutdown(wait=True)


def main() -> None:
    logger.info("Start main thread...")

    produce_scheduler_start()
    consume_thread_pool_start()

    try:
        while True:
            stop_event.wait(1)
    except (KeyboardInterrupt, SystemExit):
        logger.warning("Received interrupt signal, ready to exit...")
        produce_scheduler_start_shutdown()
        consume_thread_pool_start_shutdown()
        logger.warning("Exit the main thread.")


__all__ = [
    produce_scheduler_start,
    consume_thread_pool_start,
    produce_scheduler_start_shutdown,
    consume_thread_pool_start_shutdown,
]


if __name__ == "__main__":
    exit(main())
