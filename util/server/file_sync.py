import os
import time
import queue
import shutil
import requests
import json
import threading

from pathlib import Path, PureWindowsPath
from pathvalidate import sanitize_filename

from plugins.location_tools.repo_ops.index import Index
from util.server import logger
from util.server.file_lock import (
    get_user_repo_name,
    get_repo_lock,
    get_repo_cache_lock,
    get_repo_index_lock,
    get_repo_path,
    get_repo_cache_path,
    get_repo_index_path,
)


SUPPORT_LANGUAGE = [".java"]
WHITELIST_URL = "https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/zero-agents/LocAgent/LocAgent.whitelist"
queue_dict = {}

stop_event = threading.Event()


def process_queued():
    while not stop_event.is_set():
        repos_to_process = list(queue_dict.keys())
        for repo_cache_path in repos_to_process:
            q = queue_dict.get(repo_cache_path)
            if not q:
                continue

            batch = []
            qsize = q.qsize()
            while qsize:
                try:
                    req = q.get(timeout=1)
                    batch.append(req)
                    q.task_done()
                except queue.Empty:
                    break
                qsize -= 1

            if not batch:
                continue

            user_name, repo_name = get_user_repo_name(repo_cache_path)
            with get_repo_cache_lock(user_name, repo_name):
                for req in batch:
                    file_sync(req)
        stop_event.wait(0.1)


def file_sync(request_data):
    text = request_data["text"]
    file_path = request_data["file_path"]
    repo_path = request_data["repo_path"]
    operation_type = request_data["operation_type"]

    # windows path
    if "\\" in file_path:
        file_path = PureWindowsPath(file_path).as_posix()

    full_path = Path(repo_path) / file_path
    full_path = full_path.resolve()
    if not str(full_path).startswith(str(Path(repo_path).resolve())):
        raise PermissionError("非法路径访问")

    full_path.parent.mkdir(parents=True, exist_ok=True)

    if operation_type == "add":
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(text + "\n")
    elif operation_type == "modify":
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(text + "\n")
    elif operation_type == "delete":
        new_path = full_path.with_name(full_path.name + ".locagent_delete")
        if full_path.exists():
            full_path.unlink()
        with open(new_path, "w", encoding="utf-8") as f:
            pass


def process_update_request(request_data):
    required_fields = [
        "text",
        "user_name",
        "file_path",
        "rootPath",
        "type"
    ]
    for field in required_fields:
        if field not in request_data:
            return {"status": "error", "message": f"缺少必要字段: {field}"}, 400

    user_name = request_data["user_name"]
    root_path = request_data["rootPath"]
    repo_name = sanitize_filename(root_path, replacement_text="_")
    repo_cache_path = get_repo_cache_path(user_name, repo_name)

    # 确保队列存在
    if repo_cache_path not in queue_dict:
        queue_dict[repo_cache_path] = queue.Queue()

    # 将请求加入对应队列
    queue_dict[repo_cache_path].put(
        {
            "text": request_data["text"],
            "file_path": request_data["file_path"],
            "repo_path": repo_cache_path,
            "operation_type": request_data["type"],
        }
    )

    return {"status": "success", "message": "请求已加入处理队列"}, 200


def process_delete_index_request(request_data):
    """
    处理索引删除请求
    """
    try:
        required_fields = ['user_name', 'projectPath']
        for field in required_fields:
            if field not in request_data:
                return {"status": "error", "message": f"缺少必要字段: {field}"}, 400
        user_name = request_data['user_name']
        root_path = request_data['projectPath']
        repo_name = sanitize_filename(root_path, replacement_text="_")
        repo_path = get_repo_path(user_name, repo_name)
        repo_index_path = get_repo_index_path(user_name, repo_name)
        repo_cache_path = get_repo_cache_path(user_name, repo_name)

        with get_repo_index_lock(user_name, repo_name):
            with get_repo_lock(user_name, repo_name):
                with get_repo_cache_lock(user_name, repo_name):
                    logger.info(f"删除代码库: {repo_path}")
                    if os.path.exists(repo_path):
                        shutil.rmtree(repo_path)
                    if os.path.exists(repo_cache_path):
                        shutil.rmtree(repo_cache_path)
                    if os.path.exists(repo_index_path):
                        shutil.rmtree(repo_index_path)
                    return {"status": "success", "message": f"索引已删除: {repo_path}"}, 200

    except Exception as e:
        logger.error(f"处理请求时出错: {str(e)}")
        return {"status": "error", "message": str(e)}, 500


def process_get_config_request(request_data):
    required_fields = ['user_name']
    for field in required_fields:
        if field not in request_data:
            return {"status": "error", "message": f"缺少必要字段: {field}"}, 400
    user_name = request_data['user_name']
    white_list = get_white_list()
    file_pattern = SUPPORT_LANGUAGE
    file_pattern = json.dumps(file_pattern)
    has_authentication = "false"
    for user_id in white_list:
        if user_id in user_name:
            has_authentication = "true"
            break
    response = {
        "status": "success",
        "message": "文件模式已更新",
        "has_authentication": has_authentication,
        "file_pattern": file_pattern
        }
    return response, 200


def get_white_list():
    response = requests.get(WHITELIST_URL)
    if response.status_code == 200:
        file_content = response.text
        whitelist = [line.strip() for line in file_content.split('\n')]
        if whitelist[-1] == '':
            whitelist = whitelist[:-1]
        return whitelist
    else:
        []


file_sync_thread = threading.Thread(target=process_queued, daemon=False, name="FileSyncThread")


def file_sync_thread_start() -> None:
    file_sync_thread.start()
    logger.info(f"Startup '{file_sync_thread.name}' thread...")


def file_sync_thread_shutdown() -> None:
    logger.info(f"Shutdown '{file_sync_thread.name}' thread...")
    stop_event.set()
    file_sync_thread.join()


__all__ = [
    file_sync_thread_start,
    file_sync_thread_shutdown,
    process_update_request,
    process_get_config_request,
    process_delete_index_request,
]
