from collections import defaultdict
import os
from typing import Dict, List, <PERSON>ple
import networkx as nx
import ast
from typed_ast import ast27

from dependency_graph.lang_parser.lang_parser import (
    EDGE_TYPE_CONTAINS,
    EDGE_TYPE_IMPORTS,
    EDGE_TYPE_INHERITS,
    EDGE_TYPE_INVOKES,
    NODE_TYPE_CLASS,
    NODE_TYPE_FILE,
    NODE_TYPE_FUNCTION,
    GraphEdge,
    GraphNode,
    LangParser,
)
from dependency_graph.build_graph import (
    CodeAnalyzer,
    find_all_possible_callee,
    read_file_safely,
)


class PythonParser(LangParser):

    def __init__(self, fuzzy_search=True, global_import=False):
        self.__fuzzy_search = fuzzy_search
        self.__global_import = global_import

    def is_valid_node_types(self, node_type):
        # return node_type in self.VALID_NODE_TYPES
        pass

    def is_valid_edge_types(self, edge_type):
        # return edge_type in self.VALID_EDGE_TYPES
        pass

    # Parse the given file, use CodeAnalyzer to extract classes and helper functions from the file
    def analyze_file(self, file_path: str) -> List[GraphNode]:
        try:
            code = read_file_safely(file_path)
        except:
            raise UnicodeDecodeError

        try:
            tree = ast.parse(code, filename=file_path)
        except (SyntaxError, IndentationError, ValueError, TypeError) as e:
            try:
                tree = ast27.parse(code, filename=file_path)
            except:
                raise SyntaxError
        except:
            raise SyntaxError

        analyzer = CodeAnalyzer(file_path)
        try:
            analyzer.visit(tree)
        except RecursionError:
            pass
        return [
            GraphNode(
                name=node["name"],
                type=node["type"],
                code=node["code"],
                start_line=node["start_line"],
                end_line=node["end_line"],
            )
            for node in analyzer.nodes
        ]

    def find_imports(self, node_name: str, graph: nx.MultiDiGraph) -> List[GraphEdge]:
        node = graph.nodes[node_name]

        tree = ast.parse(node["code"], filename=node_name)

        type = node["type"]

        if type == NODE_TYPE_FILE:
            candidates = ast.walk(tree)  # find all imports
        elif type == NODE_TYPE_CLASS:
            candidates = [
                ast.iter_child_nodes(ast_node)
                for ast_node in ast.walk(tree)
                if isinstance(ast_node, ast.ClassDef) and ast_node.name == node_name
            ]  # find class imports
            candidates = [item for sublist in candidates for item in sublist]
            print(candidates.count())
            for ast_node in candidates:
                print(f"ast_node={ast_node}")
            # print(f"candidates={candidates}")
        elif type == NODE_TYPE_FUNCTION:
            candidates = ast.iter_child_nodes(ast.FunctionDef)  # find function imports

        return self.__convert_imports(
            self._do_find_imports(node_name, graph, candidates), graph
        )

    def __find_imports(
        self, node_name: str, graph: nx.MultiDiGraph, tree
    ) -> List[Dict]:
        return self.__convert_imports(
            self._do_find_imports(node_name, graph, ast.iter_child_nodes(tree)), graph
        )

    def _do_find_imports(
        self, node_name: str, graph: nx.MultiDiGraph, candidates
    ) -> List[Dict]:
        imports = []
        for node in candidates:
            if isinstance(node, ast.Import):
                # Handle 'import module' and 'import module as alias'
                for alias in node.names:
                    module_name = alias.name
                    asname = alias.asname
                    resolved_module = self.__resolve_module(module_name, graph)
                    imports.append(
                        {
                            "type": "import",
                            "module": module_name,
                            "resolved_entity": resolved_module,
                            "alias": asname,
                        }
                    )
            elif isinstance(node, ast.ImportFrom):
                # Handle 'from ... import ...' statements
                # Calculate the module name for relative imports
                if node.level == 0:
                    # Absolute import
                    module_name = node.module
                else:
                    # Relative import
                    # rel_dir = os.path.dirname(rel_path)
                    package_parts = node_name.split(os.sep)

                    # Adjust for the level of relative import
                    if len(package_parts) >= node.level:
                        package_parts = package_parts[: -node.level]
                    else:
                        package_parts = []

                    if node.module:
                        module_name = ".".join(package_parts + [node.module])
                    else:
                        module_name = ".".join(package_parts)

                import_entities = []
                for alias in node.names:
                    if alias.name == "*":
                        import_entities = [
                            {"name": "*", "resolved_entity": None, "alias": None}
                        ]
                        break
                    else:
                        entity_name = alias.name
                        asname = alias.asname
                        # resolved_entity = self.resolve_entity(
                        #     module_name, entity_name, "repo_path", graph
                        # )
                        resolved_entity = self.__resolve_entity(
                            module_name, entity_name, graph
                        )
                        import_entities.append(
                            {
                                "name": entity_name,
                                "resolved_entity": resolved_entity,
                                "alias": asname,
                            }
                        )

                # resolved_module = self.resolve_module(module_name, repo_path, graph)
                resolved_module = self.__resolve_module(module_name, graph)
                imports.append(
                    {
                        "type": "from",
                        "module": module_name,
                        "resolved_entity": resolved_module,
                        "entities": import_entities,
                    }
                )

        return imports

    def __convert_imports(self, imports, graph: nx.MultiDiGraph) -> List[GraphEdge]:
        new_imports = []
        for imp in imports:
            if "entities" not in imp:
                resolved_entity = imp["resolved_entity"]
                if graph.has_node(resolved_entity):
                    # new_imports.append((resolved_entity, imp["alias"]))
                    new_imports.append(
                        GraphEdge(
                            v_for_edge=resolved_entity,
                            type=EDGE_TYPE_IMPORTS,
                            alias=imp["alias"],
                        )
                    )
            else:
                entities = imp["entities"]

                if len(entities) == 1 and entities[0]["name"] == "*":
                    # Handle 'from module import *' as 'import module' statement
                    resolved_entity = imp["resolved_entity"]
                    if graph.has_node(resolved_entity):
                        # new_imports.append((resolved_entity, None))
                        new_imports.append(
                            GraphEdge(
                                v_for_edge=resolved_entity,
                                type=EDGE_TYPE_IMPORTS,
                                alias=None,
                            )
                        )

                    continue  # Skip further processing for 'import *'

                for entity in entities:
                    resolved_entity = entity["resolved_entity"]
                    # Entity is a submodule
                    if graph.has_node(resolved_entity):
                        # new_imports.append((resolved_entity, entity["alias"]))
                        new_imports.append(
                            GraphEdge(
                                v_for_edge=resolved_entity,
                                type=EDGE_TYPE_IMPORTS,
                                alias=entity["alias"],
                            )
                        )
                    else:
                        # Entity might be an attribute inside the module
                        resolved_entity = imp["resolved_entity"]
                        if graph.has_node(resolved_entity):
                            # new_imports.append((resolved_entity, entity["alias"]))
                            new_imports.append(
                                GraphEdge(
                                    v_for_edge=resolved_entity,
                                    type=EDGE_TYPE_IMPORTS,
                                    alias=entity["alias"],
                                )
                            )

        return new_imports

    def analyze_node(self, node: str, attributes, graph: nx.MultiDiGraph):
        # analysis invokes and inherits, add (top-level) imports edges (class/function -> class/function)
        if attributes.get("type") == NODE_TYPE_FILE:
            invocations, inheritances, imports = [], [], self.find_imports(node, graph)
        elif attributes.get("type") == NODE_TYPE_CLASS:
            invocations, inheritances, imports = self._analyze_init(node, graph)
        else:
            invocations, inheritances, imports = self._analyze_invokes(node, graph)

        global_name_dict = defaultdict(list)
        if self.__global_import:
            for node in graph.nodes():
                node_name = node.split(":")[-1].split(".")[-1]
                global_name_dict[node_name].append(node)

        # construct possible callee dict (name -> node) based on graph connectivity
        callee_nodes, callee_alias = find_all_possible_callee(node, graph)
        if self.__fuzzy_search:
            # for nodes with the same suffix, keep every nodes
            callee_name_dict = defaultdict(list)
            for callee_node in set(callee_nodes):
                callee_name = callee_node.split(":")[-1].split(".")[-1]
                callee_name_dict[callee_name].append(callee_node)
            for alias, callee_node in callee_alias.items():
                callee_name_dict[alias].append(callee_node)
        else:
            # for nodes with the same suffix, only keep the nearest node
            callee_name_dict = {
                callee_node.split(":")[-1].split(".")[-1]: callee_node
                for callee_node in callee_nodes[::-1]
            }
            callee_name_dict.update(callee_alias)

        resolved_invocations = [
            callee_name_dict.get(invocation)
            for invocation in invocations
            if invocation in callee_name_dict
        ] + [
            global_name_dict.get(invocation)
            for invocation in invocations
            if invocation not in callee_name_dict
            and self.__global_import
            and invocation in global_name_dict
        ]

        resolved_inheritances = [
            callee_name_dict.get(inheritance)
            for inheritance in inheritances
            if inheritance in callee_name_dict
        ] + [
            global_name_dict.get(inheritance)
            for inheritance in inheritances
            if inheritance not in callee_name_dict
            and self.__global_import
            and inheritance in global_name_dict
        ]

        return (
            [
                GraphEdge(v_for_edge=resolved_invocation, type=EDGE_TYPE_INVOKES)
                for invocation, resolved_invocation in zip(
                    invocations, resolved_invocations
                )
            ]
            + [
                GraphEdge(v_for_edge=resolved_inheritance, type=EDGE_TYPE_INHERITS)
                for inheritance, resolved_inheritance in zip(
                    inheritances, resolved_inheritances
                )
            ]
            + imports
        )

        # return invocations, inheritances, imports

    def _analyze_init(self, node: str, graph: nx.MultiDiGraph):
        caller_code_tree = ast.parse(graph.nodes[node]["code"])
        caller_name = node.split(":")[-1].split(".")[-1]
        # file_path = os.path.join(repo_path, node.split(":")[0])

        invocations = []
        inheritances = []
        imports = []  # TODO

        def add_invoke(func_name):
            # if func_name in callee_names:
            invocations.append(func_name)

        def add_inheritance(class_name):
            inheritances.append(class_name)

        def process_decorator_node(_decorator_node):
            if isinstance(_decorator_node, ast.Name):
                add_invoke(_decorator_node.id)
            else:
                for _sub_node in ast.walk(_decorator_node):
                    if isinstance(_sub_node, ast.Call) and isinstance(
                        _sub_node.func, ast.Name
                    ):
                        add_invoke(_sub_node.func.id)
                    elif isinstance(_sub_node, ast.Attribute):
                        add_invoke(_sub_node.attr)

        def process_inheritance_node(_inheritance_node):
            if isinstance(_inheritance_node, ast.Attribute):
                add_inheritance(_inheritance_node.attr)
            if isinstance(_inheritance_node, ast.Name):
                add_inheritance(_inheritance_node.id)

        for ast_node in ast.walk(caller_code_tree):
            if isinstance(ast_node, ast.ClassDef) and ast_node.name == caller_name:
                # add imports
                # imports = find_imports(file_path, repo_path, tree=ast_node)
                # imports = self.old_find_imports_2(file_path, repo_path, graph, tree=ast_node)
                # imports = self.find_imports_2(node, graph, tree=ast_node)
                print(f"node={node}, ast_node={ast_node}")
                imports.extend(self.__find_imports(node, graph, tree=ast_node))
                # add_imports(node, imports, graph, repo_path)

                for inheritance_node in ast_node.bases:
                    process_inheritance_node(inheritance_node)

                for decorator_node in ast_node.decorator_list:
                    process_decorator_node(decorator_node)

                for body_item in ast_node.body:
                    if (
                        isinstance(body_item, ast.FunctionDef)
                        and body_item.name == "__init__"
                    ):
                        # add imports
                        imports.extend(self.__find_imports(node, graph, tree=body_item))
                        # add_imports(node, imports, graph, repo_path)

                        for decorator_node in body_item.decorator_list:
                            process_decorator_node(decorator_node)

                        for sub_node in ast.walk(body_item):
                            if isinstance(sub_node, ast.Call):
                                if isinstance(
                                    sub_node.func, ast.Name
                                ):  # function or class
                                    add_invoke(sub_node.func.id)
                                if isinstance(
                                    sub_node.func, ast.Attribute
                                ):  # member function
                                    add_invoke(sub_node.func.attr)
                        break
                break

        return invocations, inheritances, imports

    def _analyze_invokes(self, node: str, graph: nx.MultiDiGraph):
        caller_code_tree = ast.parse(graph.nodes[node]["code"])
        caller_name = node.split(":")[-1].split(".")[-1]
        # file_path = os.path.join(repo_path, node.split(":")[0])

        # store all the invokes found
        invocations = []
        imports = []

        def add_invoke(func_name):
            # if func_name in callee_names:
            invocations.append(func_name)

        def process_decorator_node(_decorator_node):
            if isinstance(_decorator_node, ast.Name):
                add_invoke(_decorator_node.id)
            else:
                for _sub_node in ast.walk(_decorator_node):
                    if isinstance(_sub_node, ast.Call) and isinstance(
                        _sub_node.func, ast.Name
                    ):
                        add_invoke(_sub_node.func.id)
                    elif isinstance(_sub_node, ast.Attribute):
                        add_invoke(_sub_node.attr)

        def traverse_call(_node):
            for child in ast.iter_child_nodes(_node):
                if isinstance(
                    child, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)
                ):
                    # Skip inner function/class definition
                    continue
                elif isinstance(child, ast.Call):
                    if isinstance(child.func, ast.Name):
                        add_invoke(child.func.id)
                    elif isinstance(child.func, ast.Attribute):
                        add_invoke(child.func.attr)
                # Recursively traverse child nodes
                traverse_call(child)

        # Traverse AST nodes to find invokes
        for ast_node in ast.walk(caller_code_tree):
            if (
                isinstance(ast_node, (ast.FunctionDef, ast.AsyncFunctionDef))
                and ast_node.name == caller_name
            ):
                # Add imports
                # imports = find_imports(file_path, repo_path, tree=ast_node)
                imports.extend(self.__find_imports(node, graph, tree=ast_node))
                # add_imports(node, imports, graph, repo_path)

                # Traverse decorators
                for decorator_node in ast_node.decorator_list:
                    process_decorator_node(decorator_node)

                # Traverse all the invokes nodes inside the function body, excluding inner functions and classes
                traverse_call(ast_node)
                break

        return invocations, [], imports

    def __resolve_module(self, module_name: str, graph: nx.MultiDiGraph) -> str:
        """
        Resolve a module name to a file path in the repo.
        Returns the file path if found, or None if not found.
        """
        # Try to resolve as a .py file
        # module_path = os.path.join(repo_path, module_name.replace(".", "/") + ".py")
        # if os.path.isfile(module_path):
        #     return os.path.relpath(module_path, repo_path)
        node_name = module_name.replace(".", "/") + ".py"
        if graph.has_node(node_name):
            return node_name

        # Try to resolve as a package (__init__.py)
        # init_path = os.path.join(
        #     repo_path, module_name.replace(".", "/"), "__init__.py"
        # )
        # if os.path.isfile(init_path):
        #     return os.path.relpath(init_path, repo_path)
        node_name = module_name.replace(".", "/") + "/__init__.py"
        if graph.has_node(node_name):
            return node_name
        return None

    def __resolve_entity(
        self, module_name: str, entity_name: str, graph: nx.MultiDiGraph
    ) -> str:
        """
        Resolve a module name to a file path in the repo.
        Returns the file path if found, or None if not found.
        """
        resolved_entity = self.__resolve_module(f"{module_name}.{entity_name}", graph)
        if resolved_entity:
            return resolved_entity
        else:
            resolved_module = self.__resolve_module(module_name, graph)
            resolved_entity = f"{resolved_module}:{entity_name}"
            if graph.has_node(resolved_entity):
                return resolved_entity
            elif graph.has_node(resolved_module):
                return resolved_module
        return None
