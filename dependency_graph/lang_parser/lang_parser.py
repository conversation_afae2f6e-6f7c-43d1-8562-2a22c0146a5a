from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass, field
from typing import Any, Dict, List, Required, <PERSON><PERSON>
import networkx as nx
from typing import TypedDict, Optional

NODE_TYPE_DIRECTORY = "directory"
NODE_TYPE_FILE = "file"
NODE_TYPE_CLASS = "class"
NODE_TYPE_FUNCTION = "function"

EDGE_TYPE_CONTAINS = "contains"
EDGE_TYPE_INHERITS = "inherits"
EDGE_TYPE_INVOKES = "invokes"
EDGE_TYPE_IMPORTS = "imports"


@dataclass
class GraphNode:
    name: str
    type: str
    code: str = field(repr=False)
    start_line: int
    end_line: int


@dataclass
class GraphEdge:
    v_for_edge: str
    attr: dict = field(default_factory=dict, repr=True)

    def __init__(self, v_for_edge: str, **attr: Any):
        self.v_for_edge = v_for_edge
        self.attr = attr


class LangParser(ABC):

    @abstractmethod
    def is_valid_node_types(self, node_type) -> bool:
        pass

    @abstractmethod
    def is_valid_edge_types(self, edge_type) -> bool:
        pass

    @abstractmethod
    def analyze_file(self, file_path: str) -> List[GraphNode]:
        """
        Analyze the given file and return a list of nodes in file.
        """
        pass

    @abstractmethod
    def find_imports(self, node: str, graph: nx.MultiDiGraph) -> List[GraphEdge]:
        """
        Find imports of the given file node.
        """
        pass

    @abstractmethod
    def analyze_node(
        self, node: str, attributes, graph: nx.MultiDiGraph
    ) -> List[GraphEdge]:
        pass
