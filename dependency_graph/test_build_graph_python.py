import os
import tarfile
import tempfile
import shutil
import networkx as nx
import pytest
from dependency_graph.build_graph import build_graph
from util.benchmark.setup_repo import setup_repo

# def create_simple_repo():
#     temp_dir = tempfile.mkdtemp()
#     CURRENT_INSTANCE = {"repo": "astropy/astropy", "base_commit": "d5bd3f68bb6d5ce3a61bdce9883ee750d1afade5"}
#     # 获取当前文件路径的上一级目录下的repos目录
#     current_file = os.path.abspath(__file__)
#     parent_dir = os.path.dirname(os.path.dirname(current_file))
#     REPO_CLONE_DIR = os.path.join(parent_dir, 'repos')

#     return setup_repo(instance_data=CURRENT_INSTANCE, repo_base_dir=temp_dir, dataset=None, clone_dir=REPO_CLONE_DIR)

# @pytest.fixture
# def simple_repo():
#     repo_path = create_simple_repo()
#     yield repo_path
#     shutil.rmtree(repo_path)

REPO_TAR_PATH: str = os.path.abspath("repos/pylint.tar.gz")

@pytest.fixture(scope="module")
def temp_repo_dir():
    repo_dir = tempfile.mkdtemp()
    assert os.path.exists(repo_dir), f"Repo directory not found: '{repo_dir}'"
    print(f"Created temp repo directory: '{repo_dir}'")

    assert os.path.exists(REPO_TAR_PATH), f"Repo zip not found: '{REPO_TAR_PATH}'"
    assert REPO_TAR_PATH.endswith(
        ".tar.gz"
    ), f"Repo zip is not a tar.gz file: '{REPO_TAR_PATH}'"

    with tarfile.open(REPO_TAR_PATH, "r:gz") as tar:
        tar.extractall(repo_dir, filter="data")
        print(f"Extracted repo '{REPO_TAR_PATH}' to '{repo_dir}'")

    yield repo_dir

    shutil.rmtree(repo_dir)
    print(f"Removed temp repo directory: '{repo_dir}'")


@pytest.fixture(scope="module")
def temp_index_dir():
    index_dir = tempfile.mkdtemp()
    assert os.path.exists(index_dir), f"Index directory not found: '{index_dir}'"
    print(f"Created temp index directory: '{index_dir}'")

    yield index_dir

    shutil.rmtree(index_dir)
    print(f"Removed temp index directory: '{index_dir}'")

def test_build_graph_for_python(temp_repo_dir):
    graph = build_graph(temp_repo_dir)
    # 打印主要信息
    print(f"节点数: {graph.number_of_nodes()}")
    print(f"边数: {graph.number_of_edges()}")
    from collections import Counter
    node_types = [data['type'] for _, data in graph.nodes(data=True)]
    nodes_counter = Counter(node_types)
    print(f"节点类型统计: {nodes_counter}")
    edge_types = [data['type'] for _, _, data in graph.edges(data=True)]
    edge_counter = Counter(edge_types)
    print(f"边类型统计: {edge_counter}")

    assert nodes_counter['function'] == 6363
    assert nodes_counter['class'] == 2369
    assert nodes_counter['file'] == 2305
    assert nodes_counter['directory'] == 742

    assert edge_counter['invokes'] == 2324
    assert edge_counter['contains'] == 11974
    assert edge_counter['imports'] == 54
    assert edge_counter['inherits'] == 495


