import ast
from dataclasses import dataclass, field, asdict
import os
import tempfile
from typing import Any, Optional, TypedDict
import networkx as nx

from dependency_graph.build_graph import build_graph
from dependency_graph.graph_builder import GraphBuilder
from dependency_graph.lang_parser.python_parser import PythonPars<PERSON>


def create_test_project():
    """Create a temporary Java project for testing"""
    temp_dir = tempfile.mkdtemp()

    # Create directory structure
    src_dir = os.path.join(temp_dir, "astropy")
    src_config_dir = os.path.join(src_dir, "config")
    os.makedirs(src_dir, exist_ok=True)
    os.makedirs(src_config_dir, exist_ok=True)

    # Create Main.java
    __init__py = """# Licensed under a 3-clause BSD style license - see LICENSE.rst
from astropy import config as _config


class Conf(_config.ConfigNamespace):
    \"\"\"
    Configuration parameters for `astropy.time`.
    \"\"\"

    use_fast_parser = _config.ConfigItem(
        ["True", "False", "force"],
        "Use fast C parser for supported time strings formats, including ISO, "
        "ISOT, and YearDayTime. Allowed values are the 'False' (use Python parser),"
        "'True' (use C parser and fall through to Python parser if fails), and "
        "'force' (use C parser and raise exception if it fails). Note that the"
        "options are all strings.",
    )

    masked_array_type = _config.ConfigItem(
        ["astropy", "numpy"],
        'The type of masked array used for masked output data.  Can be "astropy" '
        'for `astropy.utils.masked.Masked` or "numpy" to use `numpy.ma.MaskedArray`. '
        "Note that if `astropy.units.Quantity` is produced, the output always "
        "uses `astropy.utils.masked.Masked`, since `numpy.ma.MaskedArray` does not "
        "work with quantities.",
    )
    # Create a dict of available masked classes for speed.
    # Use local imports so we do not pollute the module namespace.
    from numpy.ma import MaskedArray

    from astropy.config.configuration import ConfigItem

    _MASKED_CLASSES = {"astropy": Masked, "numpy": MaskedArray}

    @property
    def _masked_cls(self):
        \"\"\"The masked class set by ``masked_array_type``.
        \"\"\"
        from astropy.config.configuration import load_config
        load_config()
        return self._MASKED_CLASSES[self.masked_array_type]


conf = Conf()

# isort: off
from .formats import *
from .core import *

# isort: on
"""

    config__init__py = """from .configuration import *"""

    config_congfiguration_py = """
class _ConfigNamespaceMeta(type):
    pass


class ConfigNamespace(metaclass=_ConfigNamespaceMeta):
    pass


class ConfigItem:

    def __init__(
        self, defaultvalue="", description=None, cfgtype=None, module=None, aliases=None
    ):
        pass
        
def load_config():
    pass
"""

    # Write files
    __init__py_path = os.path.join(src_dir, "__init__.py")
    with open(__init__py_path, "w") as f:
        f.write(__init__py)

    config__init__py_path = os.path.join(src_config_dir, "__init__.py")
    with open(config__init__py_path, "w") as f:
        f.write(config__init__py)

    config_congfiguration_py_path = os.path.join(src_config_dir, "configuration.py")
    with open(config_congfiguration_py_path, "w") as f:
        f.write(config_congfiguration_py)

    return (
        temp_dir,
        __init__py_path,
        config__init__py_path,
        config_congfiguration_py_path,
    )


def test_analyze_file():
    _, __init__py_path, _, _config_congfiguration_py_path = create_test_project()

    nodes = PythonParser().analyze_file(__init__py_path)

    def ignore_code(node):
        node = asdict(node)
        node.pop('code')
        return node
    
    nodes = [ignore_code(node) for node in nodes]

    # assert nodes[0].pop("code")
    assert nodes[0] == {
        "name": "Conf",
        "type": "class",
        "start_line": 5,
        "end_line": 41,
    }

    # assert nodes[1].pop("code")
    assert nodes[1] == {
        "name": "Conf._masked_cls",
        "type": "function",
        "start_line": 36,
        "end_line": 41,
    }

    assert len(nodes) == 2

    nodes = PythonParser().analyze_file(_config_congfiguration_py_path)
    nodes = [ignore_code(node) for node in nodes]

    # assert nodes[0].pop("code")
    assert nodes[0] == {
        "name": "_ConfigNamespaceMeta",
        "type": "class",
        "start_line": 2,
        "end_line": 3,
    }

    # assert nodes[1].pop("code")
    assert nodes[1] == {
        "name": "ConfigNamespace",
        "type": "class",
        "start_line": 6,
        "end_line": 7,
    }

    # assert nodes[2].pop("code")
    assert nodes[2] == {
        "name": "ConfigItem",
        "type": "class",
        "start_line": 10,
        "end_line": 15,
    }

    # assert nodes[3].pop("code")
    assert nodes[3] == {
        "name": "load_config",
        "type": "function",
        "start_line": 17,
        "end_line": 18,
    }

    assert len(nodes) == 4


def test_find_imports():
    repo_path, f, *files = create_test_project()

    graph = build_graph_nodes(repo_path, [f] + files)

    imports = PythonParser().find_imports("astropy/__init__.py", graph)
    print(imports)
    assert_graph_edge(imports[0], "astropy/config/__init__.py", {"type": "imports", "alias": "_config"})
    assert_graph_edge(imports[1], "astropy/config/configuration.py:ConfigItem", {"type": "imports", "alias": None})
    assert_graph_edge(imports[2], "astropy/config/configuration.py:load_config", {"type": "imports", "alias": None})
    assert len(imports) == 3

    imports = PythonParser().find_imports("astropy/__init__.py:Conf", graph)
    print(imports)
    assert_graph_edge(imports[0], "astropy/config/configuration.py:ConfigItem", {"type": "imports", "alias": None})
    assert len(imports) == 1

    # tree = ast.parse(graph.nodes[node_name]["code"], filename=node_name)
    # candidates = ast.walk(tree)
    

    # imports = PythonParser()._do_find_imports(node_name, graph, candidates)

    # assert imports[0] == {
    #     "type": "from",
    #     "module": "astropy",
    #     "resolved_entity": "astropy/__init__.py",
    #     "entities": [
    #         {
    #             "name": "config",
    #             "resolved_entity": "astropy/config/__init__.py",
    #             "alias": "_config",
    #         }
    #     ],
    # }
    # assert imports[1] == {
    #     "type": "from",
    #     "module": "astropy.formats",
    #     "resolved_entity": None,
    #     "entities": [{"name": "*", "resolved_entity": None, "alias": None}],
    # }
    # assert imports[2] == {
    #     "type": "from",
    #     "module": "astropy.core",
    #     "resolved_entity": None,
    #     "entities": [{"name": "*", "resolved_entity": None, "alias": None}],
    # }
    # assert imports[3] == {
    #     "type": "from",
    #     "module": "numpy.ma",
    #     "resolved_entity": None,
    #     "entities": [{"name": "MaskedArray", "resolved_entity": None, "alias": None}],
    # }
    # assert imports[4] == {
    #     "type": "from",
    #     "module": "astropy.config.configuration",
    #     "resolved_entity": "astropy/config/configuration.py",
    #     "entities": [
    #         {
    #             "name": "ConfigItem",
    #             "resolved_entity": "astropy/config/configuration.py:ConfigItem",
    #             "alias": None,
    #         }
    #     ],
    # }
    # assert imports[5] == {
    #     "type": "from",
    #     "module": "astropy.config.configuration",
    #     "resolved_entity": "astropy/config/configuration.py",
    #     "entities": [
    #         {
    #             "name": "load_config",
    #             "resolved_entity": "astropy/config/configuration.py:load_config",
    #             "alias": None,
    #         }
    #     ],
    # }
    # assert len(imports) == 6


    node_name = "astropy/__init__.py:Conf"

    tree = ast.parse(graph.nodes[node_name]["code"], filename=node_name)

    for ast_node in ast.walk(tree):
        if isinstance(ast_node, ast.ClassDef):
            print(f"ast_node={ast_node.name}")
            #  imports.extend(self.__find_imports(node, graph, tree=ast_node))
            candidates = ast.iter_child_nodes(ast_node)
            # candidates = ast.walk(ast_node)
            # for c in candidates:
            #     print(f"candidate={c}")
            print(f"node_name={node_name}, candidates={candidates}")

            imports = PythonParser()._do_find_imports(node_name, graph, candidates)
            print(imports)

    node_name = "astropy/__init__.py:Conf._masked_cls"

    tree = ast.parse(graph.nodes[node_name]["code"], filename=node_name)
    
    for ast_node in ast.walk(tree):
        if isinstance(ast_node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            print(f"ast_node={ast_node.name}")
            #  imports.extend(self.__find_imports(node, graph, tree=ast_node))
            candidates = ast.iter_child_nodes(ast_node)
            # candidates = ast.walk(ast_node)
            # for c in candidates:
            #     print(f"candidate={c}")
            print(f"node_name={node_name}, candidates={candidates}")

            imports = PythonParser()._do_find_imports(node_name, graph, candidates)
            print(imports)


    assert False


def test_analyze_init():
    repo_path, *files = create_test_project()

    graph = build_graph_nodes(repo_path, files)

    # invocations, inheritances, imports = PythonParser().analyze_node(
    #     "astropy/__init__.py:Conf", attributes={"type": "class"}, graph=graph
    # )

    invocations, inheritances, imports = PythonParser()._analyze_init(
        "astropy/__init__.py:Conf", graph=graph
    )

    assert len(invocations) == 0
    assert inheritances[0] == "ConfigNamespace"
    assert len(inheritances) == 1
    # assert imports[0] == ("astropy/config/configuration.py:ConfigItem", None)
    assert_graph_edge(imports[0], "astropy/config/configuration.py:ConfigItem", {"type": "imports", "alias": None})
    # {
    #     "type": "from",
    #     "module": "numpy.ma",
    #     "resolved_entity": None,
    #     "entities": [{"name": "MaskedArray", "resolved_entity": None, "alias": None}],
    # }
    # assert imports[1] == {
    #     "type": "from",
    #     "module": "astropy.config.configuration",
    #     "resolved_entity": "astropy/config/configuration.py",
    #     "entities": [
    #         {
    #             "name": "ConfigItem",
    #             "resolved_entity": "astropy/config/configuration.py:ConfigItem",
    #             "alias": None,
    #         }
    #     ],
    # }
    assert len(imports) == 1

    assert False


def test_analyze_invokes():
    repo_path, *files = create_test_project()

    graph = build_graph_nodes(repo_path, files)

    # invocations, inheritances, imports = PythonParser().analyze_node(
    #     "astropy/__init__.py:Conf._masked_cls",
    #     attributes={"type": "function"},
    #     graph=graph,
    # )

    invocations, inheritances, imports = PythonParser()._analyze_invokes(
        "astropy/__init__.py:Conf._masked_cls", graph=graph
    )

    assert invocations[0] == "load_config"
    assert len(invocations) == 1
    assert len(inheritances) == 0
    # assert imports[0] == ("astropy/config/configuration.py:load_config", None)
    assert_graph_edge(imports[0], "astropy/config/configuration.py:load_config", {"type": "imports", "alias": None})
    # {
    #     "type": "from",
    #     "module": "astropy.config.configuration",
    #     "resolved_entity": "astropy/config/configuration.py",
    #     "entities": [
    #         {
    #             "name": "load_config",
    #             "resolved_entity": "astropy/config/configuration.py:load_config",
    #             "alias": None,
    #         }
    #     ],
    # }
    assert len(imports) == 1


def test_graph_builder():
    repo_path, *_ = create_test_project()

    old_graph = build_graph(repo_path, fuzzy_search=False, global_import=False)

    # edges = old_graph.edges(data=True)
    # for e in edges:
    #     print(e)

    graph = GraphBuilder(
        repo_path, fuzzy_search=False, global_import=False
    ).build_graph()

    nodes = list(graph.nodes(data=True))
    # for node in nodes:
    #     print(node[0])

    assert_node(nodes[0], "/", {"type": "directory"})
    assert_node(nodes[1], "astropy", {"type": "directory"})
    assert_node(nodes[2], "astropy/__init__.py", {"type": "file", "file_suffix": ".py"})
    assert_node(
        nodes[3],
        "astropy/__init__.py:Conf",
        {"type": "class", "file_suffix": ".py", "start_line": 5, "end_line": 41},
    )
    assert_node(
        nodes[4],
        "astropy/__init__.py:Conf._masked_cls",
        {"type": "function", "file_suffix": ".py", "start_line": 36, "end_line": 41},
    )
    assert_node(nodes[5], "astropy/config", {"type": "directory"})
    assert_node(
        nodes[6], "astropy/config/__init__.py", {"type": "file", "file_suffix": ".py"}
    )
    assert_node(
        nodes[7],
        "astropy/config/configuration.py",
        {"type": "file", "file_suffix": ".py"},
    )
    assert_node(
        nodes[8],
        "astropy/config/configuration.py:_ConfigNamespaceMeta",
        {"type": "class", "file_suffix": ".py", "start_line": 2, "end_line": 3},
    )
    assert_node(
        nodes[9],
        "astropy/config/configuration.py:ConfigNamespace",
        {"type": "class", "file_suffix": ".py", "start_line": 6, "end_line": 7},
    )
    assert_node(
        nodes[10],
        "astropy/config/configuration.py:ConfigItem",
        {"type": "class", "file_suffix": ".py", "start_line": 10, "end_line": 15},
    )
    assert_node(
        nodes[11],
        "astropy/config/configuration.py:load_config",
        {"type": "function", "file_suffix": ".py", "start_line": 17, "end_line": 18},
    )
    assert graph.number_of_nodes() == old_graph.number_of_nodes()

    edges = list(graph.edges(data=True))

    # i = 0
    # for e in edges:
    #     print(f'assert_edge(edges[{i}], "{e[0]}", "{e[1]}", {e[2]})')
    #     i = i + 1

    contains_edges = [e for e in edges if e[2]["type"] == "contains"]
    # i = 0
    # for e in contains_edges:
    #     print(f'assert_edge(contains_edges[{i}], "{e[0]}", "{e[1]}", {e[2]})')
    #     i = i + 1

    assert_edge(contains_edges[0], "/", "astropy", {"type": "contains"})
    assert_edge(
        contains_edges[1], "astropy", "astropy/__init__.py", {"type": "contains"}
    )
    assert_edge(contains_edges[2], "astropy", "astropy/config", {"type": "contains"})
    assert_edge(
        contains_edges[3],
        "astropy/__init__.py",
        "astropy/__init__.py:Conf",
        {"type": "contains"},
    )
    assert_edge(
        contains_edges[4],
        "astropy/__init__.py:Conf",
        "astropy/__init__.py:Conf._masked_cls",
        {"type": "contains"},
    )
    assert_edge(
        contains_edges[5],
        "astropy/config",
        "astropy/config/__init__.py",
        {"type": "contains"},
    )
    assert_edge(
        contains_edges[6],
        "astropy/config",
        "astropy/config/configuration.py",
        {"type": "contains"},
    )
    assert_edge(
        contains_edges[7],
        "astropy/config/configuration.py",
        "astropy/config/configuration.py:_ConfigNamespaceMeta",
        {"type": "contains"},
    )
    assert_edge(
        contains_edges[8],
        "astropy/config/configuration.py",
        "astropy/config/configuration.py:ConfigNamespace",
        {"type": "contains"},
    )
    assert_edge(
        contains_edges[9],
        "astropy/config/configuration.py",
        "astropy/config/configuration.py:ConfigItem",
        {"type": "contains"},
    )

    imports_edges = [e for e in edges if e[2]["type"] == "imports"]
    # i = 0
    # for e in imports_edges:
    #     print(f'assert_edge(imports_edges[{i}], "{e[0]}", "{e[1]}", {e[2]})')
    #     i = i + 1

    assert_edge(
        imports_edges[0],
        "astropy/__init__.py",
        "astropy/config/__init__.py",
        {"type": "imports", "alias": "_config"},
    )
    assert_edge(
        imports_edges[1],
        "astropy/__init__.py",
        "astropy/config/configuration.py:ConfigItem",
        {"type": "imports", "alias": None},
    )
    assert_edge(
        imports_edges[2],
        "astropy/__init__.py",
        "astropy/config/configuration.py:load_config",
        {"type": "imports", "alias": None},
    )
    assert_edge(
        imports_edges[3],
        "astropy/__init__.py:Conf",
        "astropy/config/configuration.py:ConfigItem",
        {"type": "imports", "alias": None},
    )
    assert_edge(
        imports_edges[4],
        "astropy/__init__.py:Conf._masked_cls",
        "astropy/config/configuration.py:load_config",
        {"type": "imports", "alias": None},
    )
    assert_edge(
        imports_edges[5],
        "astropy/config/__init__.py",
        "astropy/config/configuration.py",
        {"type": "imports", "alias": None},
    )

    invokes_edges = [e for e in edges if e[2]["type"] == "invokes"]
    # i = 0
    # for e in invokes_edges:
    #     print(f'assert_edge(invokes_edges[{i}], "{e[0]}", "{e[1]}", {e[2]})')
    #     i = i + 1

    inherits_edges = [e for e in edges if e[2]["type"] == "inherits"]
    # i = 0
    # for e in inherits_edges:
    #     print(f'assert_edge(inherits_edges[{i}], "{e[0]}", "{e[1]}", {e[2]})')
    #     i = i + 1

    assert_edge(
        inherits_edges[0],
        "astropy/__init__.py:Conf",
        "astropy/config/configuration.py:ConfigNamespace",
        {"type": "inherits"},
    )

    assert (
        len(contains_edges + imports_edges + invokes_edges + inherits_edges)
        == graph.number_of_edges()
    )
    assert graph.number_of_edges() == old_graph.number_of_edges()

    # assert False


def build_graph_nodes(repo_path, files):
    graph = nx.MultiDiGraph()

    for file in files:
        filename = os.path.relpath(file, repo_path)
        with open(file, "rb") as f:
            graph.add_node(filename, type="file", code=f.read())
        nodes = PythonParser().analyze_file(file)
        for node in nodes:
            graph.add_node(
                f"{filename}:{node.name}",
                type=node.type,
                code=node.code,
                start_line=node.start_line,
                end_line=node.end_line,
            )
    return graph


def assert_node(node, name, attr):
    assert node[0] == name, "expected node name = {}, got {}".format(name, node[0])
    if "code" in node[1]:
        node[1].pop("code")
    assert node[1] == attr, "expected atrr = {}, got {}".format(attr, node[1])


def assert_edge(edge, u, v, attr):
    assert edge[0] == u, "expected u = {}, got {}".format(u, edge[0])
    assert edge[1] == v, "expected v = {}, got {}".format(v, edge[1])
    assert edge[2] == attr, "expected atrr = {}, got {}".format(attr, edge[2])

def assert_graph_edge(edge, v, attr):
    assert edge.v_for_edge == v, "expected v = {}, got {}".format(v, edge.v_for_edge)
    assert edge.attr == attr, "expected atrr = {}, got {}".format(attr, edge.attr)
