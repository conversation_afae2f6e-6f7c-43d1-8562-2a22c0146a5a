import os
import time
import pytest
import shutil
import tarfile
import tempfile

from networkx import MultiDiGraph
from collections import defaultdict

from dependency_graph.build_graph_java import (
    build_graph,
    NODE_TYPE_ENUM,
    NODE_TYPE_FILE,
    NODE_TYPE_CLASS,
    NODE_TYPE_FUNCTION,
    NODE_TYPE_DIRECTORY,
    NODE_TYPE_INTERFACE,
    EDGE_TYPE_IMPORTS,
    EDGE_TYPE_INVOKES,
    EDGE_TYPE_INHERITS,
    EDGE_TYPE_CONTAINS,
    EDGE_TYPE_IMPLEMENTS,
    VALID_NODE_TYPES,
    VALID_EDGE_TYPES,
)


VALID_NODE_NUM_DICT: dict = {
    NODE_TYPE_ENUM: 30,
    NODE_TYPE_FILE: 611,
    NODE_TYPE_CLASS: 532,
    NODE_TYPE_FUNCTION: 4533,
    NODE_TYPE_DIRECTORY: 233,
    NODE_TYPE_INTERFACE: 70,
}

VALID_EDGE_NUM_DICT: dict = {
    EDGE_TYPE_IMPORTS: 4179,
    EDGE_TYPE_INVOKES: 48884,
    EDGE_TYPE_INHERITS: 109,
    EDGE_TYPE_CONTAINS: 6008,
    EDGE_TYPE_IMPLEMENTS: 41,
}

REPO_TAR_PATH: str = os.path.abspath("repos/BDR.tar.gz")


@pytest.fixture(scope="module")
def temp_repo_dir():
    repo_dir = tempfile.mkdtemp()
    assert os.path.exists(repo_dir), f"Repo directory not found: '{repo_dir}'"
    print(f"Created temp repo directory: '{repo_dir}'")

    assert os.path.exists(REPO_TAR_PATH), f"Repo zip not found: '{REPO_TAR_PATH}'"
    assert REPO_TAR_PATH.endswith(
        ".tar.gz"
    ), f"Repo zip is not a tar.gz file: '{REPO_TAR_PATH}'"

    with tarfile.open(REPO_TAR_PATH, "r:gz") as tar:
        tar.extractall(repo_dir, filter="data")
        print(f"Extracted repo '{REPO_TAR_PATH}' to '{repo_dir}'")

    yield repo_dir

    shutil.rmtree(repo_dir)
    print(f"Removed temp repo directory: '{repo_dir}'")


@pytest.fixture(scope="module")
def temp_index_dir():
    index_dir = tempfile.mkdtemp()
    assert os.path.exists(index_dir), f"Index directory not found: '{index_dir}'"
    print(f"Created temp index directory: '{index_dir}'")

    yield index_dir

    shutil.rmtree(index_dir)
    print(f"Removed temp index directory: '{index_dir}'")


def show_graph_info(graph: MultiDiGraph) -> None:

    node_dict = defaultdict(int)
    edge_dict = defaultdict(int)

    for node, attributes in graph.nodes(data=True):
        node_type = attributes.get("type", "")
        if node_type:
            node_dict[node_type] += 1
        else:
            node_dict["Unknown"] += 1

    node_dict = dict(sorted(node_dict.items()))

    print(f"\n{"Total nodes":<13} = {graph.number_of_nodes():>6}")
    for item in node_dict:
        print(f" - {item:<10} = {node_dict[item]:>6}")

    for u, v, attributes in graph.edges(data=True):
        edge_type = attributes.get("type", "")
        if edge_type:
            edge_dict[edge_type] += 1
        else:
            edge_dict["Unknown"] += 1

    edge_dict = dict(sorted(edge_dict.items()))

    print(f"\n{"Total edges":<13} = {graph.number_of_edges():>6}")
    for item in edge_dict:
        print(f" - {item:<10} = {edge_dict[item]:>6}")


def check_graph_nodes(graph: MultiDiGraph) -> None:
    assert not graph is None, "Graph is None"

    node_num = graph.number_of_nodes()
    node_valid_num = sum(VALID_NODE_NUM_DICT.values())
    assert (
        node_num == node_valid_num
    ), f"Total number of nodes does not match, expected {node_valid_num}, got {node_num}."

    node_num_dict = defaultdict(int)

    for node, attributes in graph.nodes(data=True):
        node_type = attributes.get("type", "")
        assert node_type in VALID_NODE_TYPES, f"Invalid node type found: {node_type}"
        node_num_dict[node_type] += 1

    for node_type in VALID_NODE_NUM_DICT:
        assert (
            node_num_dict[node_type] == VALID_NODE_NUM_DICT[node_type]
        ), f"Invalid number of nodes for '{node_type}' type, , expected {VALID_NODE_NUM_DICT[node_type]}, got {node_num_dict[node_type]}."


def check_graph_edges(graph: MultiDiGraph) -> None:
    assert not graph is None, "Graph is None"

    edge_num = graph.number_of_edges()
    edge_valid_num = sum(VALID_EDGE_NUM_DICT.values())
    assert (
        edge_num == edge_valid_num
    ), f"Total number of edges does not match, expected {edge_valid_num}, got {edge_num}."

    edge_num_dict = defaultdict(int)

    for _, _, attributes in graph.edges(data=True):
        edge_type = attributes.get("type", "")
        assert edge_type in VALID_EDGE_TYPES, f"Invalid edge type found: {edge_type}"
        edge_num_dict[edge_type] += 1

    for edge_type in VALID_EDGE_NUM_DICT:
        assert (
            edge_num_dict[edge_type] == VALID_EDGE_NUM_DICT[edge_type]
        ), f"Invalid number of edges for '{edge_type}' type, , expected {VALID_EDGE_NUM_DICT[edge_type]}, got {edge_num_dict[edge_type]}."


def find_java_files(repo_path: str) -> list[str]:
    """Find all Java files in the source repository"""
    java_files: list = []
    for root, _, files in os.walk(repo_path):
        for file in files:
            if file.endswith(".java"):
                file_path = os.path.join(root, file)
                java_files.append(file_path)

    return java_files


def split_files_into_batches(repo_path: str) -> list[str]:
    """Split Java files into 3 temporary folders"""

    # Find all Java files
    java_files = find_java_files(repo_path)
    assert len(java_files) > 0, f"No Java files found in '{repo_path}'"
    print(f"Found {len(java_files)} Java files in '{repo_path}'")

    batch_size = len(java_files) // 3
    remainder = len(java_files) % 3

    repo_dir = os.path.dirname(repo_path)
    repo_name = os.path.basename(repo_path)

    start_idx = 0
    batch_dirs = []

    for i in range(3):
        # Calculate batch size for this iteration
        current_batch_size = batch_size + (1 if i < remainder else 0)
        end_idx = start_idx + current_batch_size

        # Create tmp directory
        batch_dir = os.path.join(repo_dir, f"{repo_name}_Batch_{i+1}")
        os.makedirs(batch_dir, exist_ok=True)
        assert os.path.exists(
            batch_dir
        ), f"Failed to create temporary directory: '{batch_dir}'"
        batch_dirs.append(batch_dir)

        # Copy files to tmp directory maintaining structure
        batch_files = java_files[start_idx:end_idx]

        for file_path in batch_files:
            # Calculate relative path from source repo
            dest_path = os.path.join(batch_dir, os.path.relpath(file_path, repo_path))

            # Create directory structure
            dest_dir = os.path.dirname(dest_path)
            os.makedirs(dest_dir, exist_ok=True)
            assert os.path.exists(dest_dir), f"Failed to create directory: '{dest_dir}'"

            # Copy file
            shutil.copy2(file_path, dest_path)
            assert os.path.exists(
                dest_path
            ), f"Failed to copy file: '{file_path}' to '{dest_path}'"

        print(f"Created '{batch_dir}' with {len(batch_files)} files")
        start_idx = end_idx

    return batch_dirs


def test_build_graph_full(temp_repo_dir):

    repo_path = os.path.join(
        temp_repo_dir, os.path.basename(REPO_TAR_PATH).split(".")[0]
    )
    assert os.path.exists(repo_path), f"Repo directory not found: '{repo_path}'"

    start_time = time.time()
    graph = build_graph(graph=None, repo_path=repo_path)
    print(f"Time taken to build graph: {(time.time() - start_time):.2f}s")

    show_graph_info(graph)
    check_graph_nodes(graph)
    check_graph_edges(graph)


def test_build_graph_increment(temp_repo_dir):

    repo_path = os.path.join(
        temp_repo_dir, os.path.basename(REPO_TAR_PATH).split(".")[0]
    )
    assert os.path.exists(repo_path), f"Repo directory not found: '{repo_path}'"

    # Split files into 3 batches
    batch_dirs = split_files_into_batches(repo_path)
    assert len(batch_dirs) == 3, f"Expected 3 batches, but got {len(batch_dirs)}"

    graph = None
    total_time = 0

    # Process each batch
    for i, batch_dir in enumerate(batch_dirs, 1):
        start_time = time.time()
        graph = build_graph(graph=graph, repo_path=batch_dir)
        end_time = time.time()
        batch_time = end_time - start_time
        total_time += batch_time
        print(f"Processing Batch {i}/3 took {batch_time:.2f}s")

    print(f"Time taken to build graph: {total_time:.2f}s")

    # Clean target directory
    for _, batch_dir in enumerate(batch_dirs):
        shutil.rmtree(batch_dir)
        print(f"Removed temporary batch directory: {batch_dir}")

    show_graph_info(graph)
    check_graph_nodes(graph)
    check_graph_edges(graph)


"""
# 进入项目目录
cd LocAgent

# 运行测试并忽略 FutureWarning 警告
PYTHONPATH="${PWD}" pytest dependency_graph/test_build_graph_java.py -W ignore::FutureWarning

# 运行测试并显示详细输出
PYTHONPATH="${PWD}" pytest dependency_graph/test_build_graph_java.py -s

# 运行测试并显示详细输出和测试用例名称
PYTHONPATH="${PWD}" pytest dependency_graph/test_build_graph_java.py -v

# 运行测试并显示详细输出、测试用例名称、测试用例执行时间以及测试用例的setup部分
PYTHONPATH="${PWD}" pytest dependency_graph/test_build_graph_java.py --setup-show
"""
