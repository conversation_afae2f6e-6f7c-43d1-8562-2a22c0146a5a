import networkx as nx

graph = nx.MultiDiGraph()
graph.add_node("A", type="directory")
graph.add_node("B", type="file")
graph.add_node("C", type="directory")
graph.add_node("D", type="file")
graph.add_edge("A", "B", type="contains")
graph.add_edge("A", "C", type="contains")
graph.add_edge("C", "D", type="contains")

print(graph.nodes(data=True))
print(graph.edges(data=True))


print([id for id in graph.successors("A")])
print(any([id for id in graph.successors("A") if graph.nodes[id]['type'] == 'file']))


def contains_file_node(graph, node):
    """
    判断给定节点及其所有后继节点是否包含file类型的节点

    Args:
        graph: NetworkX MultiDiGraph对象
        node: 要检查的起始节点

    Returns:
        bool: 如果节点本身或其任何后继节点包含file类型节点则返回True，否则返回False
    """
    # 检查节点是否存在于图中
    if not graph.has_node(node):
        return False

    # 检查当前节点是否为file类型
    if graph.nodes[node].get('type') == 'file':
        return True

    # 使用深度优先搜索遍历所有后继节点
    visited = set()
    stack = [node]

    while stack:
        current = stack.pop()
        if current in visited:
            continue
        visited.add(current)

        # 检查当前节点是否为file类型
        if graph.nodes[current].get('type') == 'file':
            return True

        # 将所有后继节点加入栈中
        for successor in graph.successors(current):
            if successor not in visited:
                stack.append(successor)

    return False


# 测试函数
print("\n=== 测试 contains_file_node 函数 ===")
print(f"节点A及其后继节点是否包含file类型: {contains_file_node(graph, 'A')}")
print(f"节点B及其后继节点是否包含file类型: {contains_file_node(graph, 'B')}")
print(f"节点C及其后继节点是否包含file类型: {contains_file_node(graph, 'C')}")
print(f"节点D及其后继节点是否包含file类型: {contains_file_node(graph, 'D')}")

# 测试不存在的节点
print(f"不存在的节点E: {contains_file_node(graph, 'E')}")

# 创建一个更复杂的测试图
print("\n=== 创建更复杂的测试图 ===")
complex_graph = nx.MultiDiGraph()
complex_graph.add_node("root", type="directory")
complex_graph.add_node("dir1", type="directory")
complex_graph.add_node("dir2", type="directory")
complex_graph.add_node("dir3", type="directory")
complex_graph.add_node("file1", type="file")
complex_graph.add_node("class1", type="class")
complex_graph.add_node("function1", type="function")

# 构建层次结构: root -> dir1 -> dir2 -> dir3 -> class1 -> function1
# 以及 root -> file1
complex_graph.add_edge("root", "dir1", type="contains")
complex_graph.add_edge("root", "file1", type="contains")
complex_graph.add_edge("dir1", "dir2", type="contains")
complex_graph.add_edge("dir2", "dir3", type="contains")
complex_graph.add_edge("dir3", "class1", type="contains")
complex_graph.add_edge("class1", "function1", type="contains")

print("复杂图的节点:", complex_graph.nodes(data=True))
print("复杂图的边:", complex_graph.edges(data=True))

print(f"root节点及其后继节点是否包含file类型: {contains_file_node(complex_graph, 'root')}")
print(f"dir1节点及其后继节点是否包含file类型: {contains_file_node(complex_graph, 'dir1')}")
print(f"dir2节点及其后继节点是否包含file类型: {contains_file_node(complex_graph, 'dir2')}")
print(f"class1节点及其后继节点是否包含file类型: {contains_file_node(complex_graph, 'class1')}")
print(f"file1节点及其后继节点是否包含file类型: {contains_file_node(complex_graph, 'file1')}")
