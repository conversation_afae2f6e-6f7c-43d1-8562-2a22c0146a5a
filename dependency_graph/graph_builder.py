from dataclasses import asdict
import os
from typing import List
import networkx as nx


from dependency_graph.lang_parser.lang_parser import (
    EDGE_TYPE_CONTAINS,
    EDGE_TYPE_IMPORTS,
    EDGE_TYPE_INHERITS,
    EDGE_TYPE_INVOKES,
    NODE_TYPE_CLASS,
    NODE_TYPE_DIRECTORY,
    NODE_TYPE_FILE,
    NODE_TYPE_FUNCTION,
    LangParser,
)
from dependency_graph.lang_parser.python_parser import PythonParser


VERSION = "v2.3"

_SUPPORT_LANGUAGES = [".py", ".java"]


def get_lang_parser(
    file_suffix: str, fuzzy_search=True, global_import=False
) -> LangParser:
    if file_suffix == ".py":
        return PythonParser(fuzzy_search=fuzzy_search, global_import=global_import)
    elif file_suffix == ".java":
        return None  # TODO


# def valid_file_suffix(file_suffix: str) -> bool:
#     return file_suffix in __SUPPORT_LANGUAGES


def get_file_suffix(file: str) -> str:
    return os.path.splitext(file)[1]


class GraphBuilder:
    def __init__(self, repo_path, fuzzy_search=True, global_import=False):
        self.__repo_path = repo_path
        self.__fuzzy_search = fuzzy_search
        self.__global_import = global_import
        pass

    def __is_skip_dir(self, dirname):
        SKIP_DIRS = [".github", ".git"]
        for skip_dir in SKIP_DIRS:
            if skip_dir in dirname:
                return True
        return False

    def build_graph(self) -> nx.MultiDiGraph:
        graph = nx.MultiDiGraph()
        file_nodes = {}

        ## add nodes
        graph.add_node("/", type=NODE_TYPE_DIRECTORY)
        dir_stack: List[str] = []
        dir_include_stack: List[bool] = []
        for root, _, files in os.walk(self.__repo_path):

            # add directory nodes and edges
            dirname = os.path.relpath(root, self.__repo_path)
            if dirname == ".":
                dirname = "/"
            elif self.__is_skip_dir(dirname):
                continue
            else:
                graph.add_node(dirname, type=NODE_TYPE_DIRECTORY)
                parent_dirname = os.path.dirname(dirname)
                if parent_dirname == "":
                    parent_dirname = "/"
                graph.add_edge(parent_dirname, dirname, type=EDGE_TYPE_CONTAINS)

            # in reverse step, remove directories that do not contain .py file
            while len(dir_stack) > 0 and not dirname.startswith(dir_stack[-1]):
                if not dir_include_stack[-1]:
                    # print('remove', dir_stack[-1])
                    graph.remove_node(dir_stack[-1])
                dir_stack.pop()
                dir_include_stack.pop()
            if dirname != "/":
                dir_stack.append(dirname)
                dir_include_stack.append(False)

            dir_has_file = False
            for file in files:
                file_suffix = get_file_suffix(file)

                if file_suffix not in _SUPPORT_LANGUAGES:
                    continue

                dir_has_file = True

                parser = get_lang_parser(
                    file_suffix,
                    fuzzy_search=self.__fuzzy_search,
                    global_import=self.__global_import,
                )

                # add file nodes
                try:
                    file_path = os.path.join(root, file)
                    filename = os.path.relpath(file_path, self.__repo_path)
                    if os.path.islink(file_path):
                        continue
                    else:
                        with open(file_path, "r") as f:
                            file_content = f.read()

                    graph.add_node(
                        filename,
                        type=NODE_TYPE_FILE,
                        code=file_content,
                        file_suffix=file_suffix,
                    )
                    file_nodes[filename] = file_path

                    nodes = parser.analyze_file(file_path)
                except (UnicodeDecodeError, SyntaxError):
                    # Skip the file that cannot decode or parse
                    print(f"skip {file_path}")
                    continue

                # add function/class nodes
                for node in nodes:
                    full_name = f"{filename}:{node.name}"
                    graph.add_node(
                        full_name,
                        type=node.type,
                        code=node.code,
                        start_line=node.start_line,
                        end_line=node.end_line,
                        file_suffix=file_suffix,
                    )

                # add edges with type=contains
                graph.add_edge(dirname, filename, type=EDGE_TYPE_CONTAINS)
                for node in nodes:
                    full_name = f"{filename}:{node.name}"
                    name_list = node.name.split(".")
                    if len(name_list) == 1:
                        graph.add_edge(filename, full_name, type=EDGE_TYPE_CONTAINS)
                    else:
                        parent_name = ".".join(name_list[:-1])
                        full_parent_name = f"{filename}:{parent_name}"
                        graph.add_edge(
                            full_parent_name, full_name, type=EDGE_TYPE_CONTAINS
                        )

            # keep all parent directories
            if dir_has_file:
                for i in range(len(dir_include_stack)):
                    dir_include_stack[i] = True

        # check last traversed directory
        while len(dir_stack) > 0:
            if not dir_include_stack[-1]:
                graph.remove_node(dir_stack[-1])
            dir_stack.pop()
            dir_include_stack.pop()

        # add imports edges (file -> class/function)
        for filename, filepath in file_nodes.items():
            file_suffix = get_file_suffix(filepath)
            parser = get_lang_parser(
                file_suffix,
                fuzzy_search=self.__fuzzy_search,
                global_import=self.__global_import,
            )
            try:
                imports = parser.analyze_node(filename, {"type": NODE_TYPE_FILE}, graph)
            except SyntaxError:
                continue
            # self.__add_imports(filename, imports, graph)
            for edge in imports:
                graph.add_edge(filename, edge.v_for_edge, **edge.attr)

        ## add edges start from class/function
        for node, attributes in graph.nodes(data=True):
            if attributes.get("type") not in [NODE_TYPE_CLASS, NODE_TYPE_FUNCTION]:
                continue

            file_suffix = attributes.get("file_suffix")
            parser = get_lang_parser(
                file_suffix,
                fuzzy_search=self.__fuzzy_search,
                global_import=self.__global_import,
            )

            # analysis invokes and inherits, add (top-level) imports edges (class/function -> class/function)
            # invocations, inheritances, imports = parser.analyze_node(
            #     node, attributes, graph
            # )
            edges = parser.analyze_node(node, attributes, graph)
            for edge in edges:
                if edge.attr["type"] == EDGE_TYPE_INHERITS:
                    print(edge)
                graph.add_edge(node, edge.v_for_edge, **edge.attr)

            # self.__add_imports(node, imports, graph)

            # # add invokes edges (class/function -> class/function)
            # self.__add_invocations(node, invocations, graph)

            # # add inherits edges (class -> class)
            # self.__add_inheritances(node, inheritances, graph)

        return graph

    def __add_imports(self, node, imports, graph: nx.MultiDiGraph):
        s = set()
        for entity, alias in imports:
            key = (entity, alias)
            if key not in s:
                if graph.has_node(entity):
                    graph.add_edge(node, entity, type=EDGE_TYPE_IMPORTS, alias=alias)
                    s.add(key)
                else:
                    print(f"Discard import={entity} for {node} as entity not found.")

    def __add_invocations(self, node, invocations, graph: nx.MultiDiGraph):
        for callee_node in set(invocations):
            if isinstance(callee_node, list):
                for callee in callee_node:
                    if graph.has_node(callee):
                        graph.add_edge(node, callee, type=EDGE_TYPE_INVOKES)
                    else:
                        print(
                            f"Discard invoke={callee} for {node} as callee not found."
                        )
            else:
                if graph.has_node(callee_node):
                    graph.add_edge(node, callee_node, type=EDGE_TYPE_INVOKES)
                else:
                    print(
                        f"Discard invoke={callee_node} for {node} as callee not found."
                    )

    def __add_inheritances(self, node, inheritances, graph: nx.MultiDiGraph):
        for callee_node in set(inheritances):
            if isinstance(callee_node, list):
                for callee in callee_node:
                    if graph.has_node(callee):
                        graph.add_edge(node, callee, type=EDGE_TYPE_INHERITS)
                    else:
                        print(
                            f"Discard inherit={callee} for {node} as callee not found."
                        )
            else:
                if graph.has_node(callee_node):
                    graph.add_edge(node, callee_node, type=EDGE_TYPE_INHERITS)
                else:
                    print(
                        f"Discard inherit={callee_node} for {node} as callee not found."
                    )
