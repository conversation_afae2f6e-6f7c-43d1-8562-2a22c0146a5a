{"id": 1, "repo": "fasterxml/jackson-dataformat-xml", "instance_id": "fasterxml__jackson-dataformat-xml-644", "base_commit": "b782f4b9559ece1b6178cbeafa8acffb0ab9d0f0", "patch": "", "problem_statement": "Fix #643: Add `ToXmlGenerator.Feature` or allowing XML Schema/JAXB compatible Infinity representation\nThis PR adds a `ToXmlGenerator.Feature` to use XML Schema-compatible representation for floating-point infinity.\r\n\r\nFixes #643. #643 XML serialization of floating-point infinity is incompatible with JAXB and XML Schema\r\n", "edit_functions": ["release-notes/CREDITS-2.x", "release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/dataformat/xml/ser/ToXmlGenerator.java"]}
{"id": 2, "repo": "fasterxml/jackson-dataformat-xml", "instance_id": "fasterxml__jackson-dataformat-xml-638", "base_commit": "ac00d648e9b424f4b6c4d7aaaa23abf50adc1b5a", "patch": "", "problem_statement": "Fix #637: merge namespace information properly\nNone #637 `JacksonXmlAnnotationIntrospector.findNamespace()` should properly merge namespace information\r\n", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/dataformat/xml/JacksonXmlAnnotationIntrospector.java"]}
{"id": 3, "repo": "fasterxml/jackson-dataformat-xml", "instance_id": "fasterxml__jackson-dataformat-xml-590", "base_commit": "a18b8cd98e94660dcac19bd2cd11f376705d7745", "patch": "", "problem_statement": "Fix #578 (@JsonAppend properties serialized twice)\nNone #578 `XmlMapper` serializes `@JsonAppend` property twice\r\n", "edit_functions": ["release-notes/VERSION-2.x", "src/main/java/com/fasterxml/jackson/dataformat/xml/JacksonXmlAnnotationIntrospector.java"]}
{"id": 4, "repo": "fasterxml/jackson-dataformat-xml", "instance_id": "fasterxml__jackson-dataformat-xml-544", "base_commit": "6c03760102474a0e38f0f52cdaef2a88e7133598", "patch": "", "problem_statement": "Support unwrapping in `@JsonRawValue` serialization\nFixes #545  #545 `@JacksonXmlText` does not work when paired with `@JsonRawValue`\r\n", "edit_functions": ["src/main/java/com/fasterxml/jackson/dataformat/xml/ser/ToXmlGenerator.java"]}
{"id": 5, "repo": "fasterxml/jackson-dataformat-xml", "instance_id": "fasterxml__jackson-dataformat-xml-531", "base_commit": "f406e23f5e15efb3d930e826204c06e00a23f8e3", "patch": "", "problem_statement": "Add mechanism for processing invalid XML names (transforming to valid ones)\nThis commit introduces the `PROCESS_ESCAPED_MALFORMED_TAGS` and\r\n`ESCAPE_MALFORMED_TAGS` features that control whether invalid\r\ntag names will be escaped with an attribute.\r\n\r\nfixes #523\r\nfixes #524 #524 Dollars in POJO property names are not escaped on serialization\r\n", "edit_functions": ["src/main/java/com/fasterxml/jackson/dataformat/xml/XmlFactory.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/XmlFactoryBuilder.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/XmlMapper.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/XmlTagProcessor.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/XmlTagProcessors.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/deser/FromXmlParser.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/deser/XmlTokenStream.java", "src/main/java/com/fasterxml/jackson/dataformat/xml/ser/ToXmlGenerator.java"]}
