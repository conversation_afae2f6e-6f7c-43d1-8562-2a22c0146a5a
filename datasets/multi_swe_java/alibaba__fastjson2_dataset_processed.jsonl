{"id": 1, "repo": "alibaba/fastjson2", "instance_id": "alibaba__fastjson2-2775", "base_commit": "12b40c7ba3e7c30e35977195770c80beb34715c5", "patch": "", "problem_statement": "add new method JSON.parseObject(String, Type, JSONReader.Context)\n### What this PR does / why we need it?\r\n\r\nadd new method JSON.parseObject(String, Type, JSONReader.Context), ref #2774\r\n\r\nclose #2774\r\n\r\n### Summary of your change\r\n\r\n\r\n\r\n#### Please indicate you've done the following:\r\n\r\n- [ ] Made sure tests are passing and test coverage is added if needed.\r\n- [ ] Made sure commit message follow the rule of [Conventional Commits specification](https://www.conventionalcommits.org/).\r\n- [ ] Considered the docs impact and opened a new docs issue or PR with docs changes if needed.\r\n #2774 [FEATURE] 需要个新接口：JSON.parseObject(String, Type, JSONReader.Context)\r\n", "edit_functions": ["core/src/main/java/com/alibaba/fastjson2/JSON.java"]}
{"id": 2, "repo": "alibaba/fastjson2", "instance_id": "alibaba__fastjson2-2559", "base_commit": "45cc6b34343bc133a03c632d6c9dd4a2d895e2e9", "patch": "", "problem_statement": "fix #2558\nplease refer to this [issue](https://github.com/alibaba/fastjson2/issues/2558) #2558 [FEATURE]org.bson.types.Decimal128转Double时会报错\r\n", "edit_functions": ["core/src/main/java/com/alibaba/fastjson2/util/TypeUtils.java"]}
{"id": 3, "repo": "alibaba/fastjson2", "instance_id": "alibaba__fastjson2-2285", "base_commit": "27ca2b45c33cd362fa35613416f5d62ff9567921", "patch": "", "problem_statement": "fix: enum JSONType#serializer not work #2269\n### What this PR does / why we need it?\r\n\r\nfix #2269 \r\n\r\n### Summary of your change\r\n\r\n#### Please indicate you've done the following:\r\n\r\n- [x] Made sure tests are passing and test coverage is added if needed.\r\n- [ ] Made sure commit message follow the rule of [Conventional Commits specification](https://www.conventionalcommits.org/).\r\n- [ ] Considered the docs impact and opened a new docs issue or PR with docs changes if needed.\r\n #2269 fastjson2 （version 2.0.46）， JSONType注解指定自定义序列化无效[BUG]\r\n", "edit_functions": ["core/src/main/java/com/alibaba/fastjson2/writer/ObjectWriterBaseModule.java"]}
{"id": 4, "repo": "alibaba/fastjson2", "instance_id": "alibaba__fastjson2-2097", "base_commit": "3f6275bcc3cd40a57f6d257cdeec322d1b9ae06d", "patch": "", "problem_statement": "fix parse reference incorrect of java.util.Arrays$ArrayList\n### What this PR does / why we need it?\r\nfix #2096\r\n\r\n\r\n### Summary of your change\r\n\r\n\r\n\r\n#### Please indicate you've done the following:\r\n\r\n- [x] Made sure tests are passing and test coverage is added if needed.\r\n- [x] Made sure commit message follow the rule of [Conventional Commits specification](https://www.conventionalcommits.org/).\r\n- [x] Considered the docs impact and opened a new docs issue or PR with docs changes if needed.\r\n #2096 [BUG] reference in java.util.Arrays$ArrayList(CLASS_ARRAYS_LIST) deserialization wrong\r\n", "edit_functions": ["core/src/main/java/com/alibaba/fastjson2/reader/ObjectReaderImplList.java"]}
{"id": 5, "repo": "alibaba/fastjson2", "instance_id": "alibaba__fastjson2-1245", "base_commit": "6648b96c0162c222467eb44bac30a9d59392c7ff", "patch": "", "problem_statement": "#1244 fix EnumSet deserialization fail\n### What this PR does / why we need it?\r\nfix #1244 EnumSet deserialization fail\r\n\r\n### Summary of your change\r\n\r\n\r\n\r\n#### Please indicate you've done the following:\r\n\r\n- [ ] Made sure tests are passing and test coverage is added if needed.\r\n- [ ] Made sure commit message follow the rule of [Conventional Commits specification](https://www.conventionalcommits.org/).\r\n- [ ] Considered the docs impact and opened a new docs issue or PR with docs changes if needed.\r\n #1244 [BUG]spring/dubbo序列化，对EnumSet类型反序列化失败\r\n", "edit_functions": ["core/src/main/java/com/alibaba/fastjson2/filter/ContextAutoTypeBeforeHandler.java", "core/src/main/java/com/alibaba/fastjson2/reader/ObjectReaderImplList.java"]}
{"id": 6, "repo": "alibaba/fastjson2", "instance_id": "alibaba__fastjson2-82", "base_commit": "3aed80608b36c310d0fe5f240f49d670b3638698", "patch": "", "problem_statement": "bug fixed for json isValid,fix #81\nNone #81 判断字符串是否为JSON对象或JSON数组有问题\r\n", "edit_functions": ["core/src/main/java/com/alibaba/fastjson2/JSON.java"]}
