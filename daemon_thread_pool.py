import threading
import time
import queue
from typing import Callable, Any, Optional
from concurrent.futures import Future


class DaemonThreadPool:
    """
    A thread pool implementation using daemon threads.
    Daemon threads will automatically terminate when the main program exits.
    """
    
    def __init__(self, max_workers: int = 4, thread_name_prefix: str = "DaemonWorker"):
        """
        Initialize the daemon thread pool.
        
        Args:
            max_workers: Maximum number of worker threads
            thread_name_prefix: Prefix for thread names
        """
        self.max_workers = max_workers
        self.thread_name_prefix = thread_name_prefix
        self._task_queue = queue.Queue()
        self._workers = []
        self._shutdown = False
        self._lock = threading.Lock()
        
        # Start worker threads
        self._start_workers()
    
    def _start_workers(self):
        """Start the worker daemon threads."""
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker,
                name=f"{self.thread_name_prefix}-{i}",
                daemon=True  # Set as daemon thread
            )
            worker.start()
            self._workers.append(worker)
    
    def _worker(self):
        """Worker thread function that processes tasks from the queue."""
        while not self._shutdown:
            try:
                # Get task from queue with timeout
                task_item = self._task_queue.get(timeout=1.0)
                if task_item is None:  # Shutdown signal
                    break
                
                func, args, kwargs, future = task_item
                
                if not future.cancelled():
                    try:
                        result = func(*args, **kwargs)
                        future.set_result(result)
                    except Exception as e:
                        future.set_exception(e)
                
                self._task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                if not self._shutdown:
                    print(f"Worker thread error: {e}")
    
    def submit(self, func: Callable, *args, **kwargs) -> Future:
        """
        Submit a callable to be executed with the given arguments.
        
        Args:
            func: The callable to execute
            *args: Positional arguments for the callable
            **kwargs: Keyword arguments for the callable
            
        Returns:
            Future object representing the execution of the callable
        """
        if self._shutdown:
            raise RuntimeError("ThreadPool is shut down")
        
        future = Future()
        task_item = (func, args, kwargs, future)
        self._task_queue.put(task_item)
        return future
    
    def map(self, func: Callable, iterable) -> list:
        """
        Apply func to each element of iterable, collecting results in a list.
        
        Args:
            func: Function to apply
            iterable: Iterable of items to process
            
        Returns:
            List of results
        """
        futures = [self.submit(func, item) for item in iterable]
        return [future.result() for future in futures]
    
    def shutdown(self, wait: bool = True):
        """
        Shutdown the thread pool.
        
        Args:
            wait: If True, wait for all tasks to complete before returning
        """
        with self._lock:
            if self._shutdown:
                return
            
            self._shutdown = True
            
            # Signal all workers to stop
            for _ in self._workers:
                self._task_queue.put(None)
        
        if wait:
            # Wait for all workers to finish
            for worker in self._workers:
                if worker.is_alive():
                    worker.join()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.shutdown(wait=True)
    
    @property
    def active_threads(self) -> int:
        """Get the number of active worker threads."""
        return sum(1 for worker in self._workers if worker.is_alive())
    
    def is_daemon(self) -> bool:
        """Check if all worker threads are daemon threads."""
        return all(worker.daemon for worker in self._workers)


# Example usage and testing
def example_task(task_id: int, delay: float = 1.0) -> str:
    """Example task function for testing."""
    print(f"Task {task_id} starting on thread {threading.current_thread().name}")
    time.sleep(delay)
    result = f"Task {task_id} completed"
    print(result)
    return result


def main():
    """Example usage of DaemonThreadPool."""
    print("Starting DaemonThreadPool example...")
    
    # Create daemon thread pool
    with DaemonThreadPool(max_workers=3, thread_name_prefix="ExampleWorker") as pool:
        print(f"Thread pool created with {pool.active_threads} active threads")
        print(f"All threads are daemon: {pool.is_daemon()}")
        
        # Submit individual tasks
        futures = []
        for i in range(5):
            future = pool.submit(example_task, i, 0.5)
            futures.append(future)
        
        # Wait for results
        results = [future.result() for future in futures]
        print(f"Results: {results}")
        
        # Use map function
        map_results = pool.map(lambda x: f"Mapped task {x}", range(3))
        print(f"Map results: {map_results}")
    
    print("Thread pool context exited - daemon threads will terminate automatically")


if __name__ == "__main__":
    main()
