from util.server.retriever import load_repo_ops, retrieve_code as _retrieve_code
from util.server.file_sync import (
    file_sync_thread_start,
    file_sync_thread_shutdown,
    process_update_request,
    process_delete_index_request,
    process_get_config_request,
)
from util.server.build_worker import (
    produce_scheduler_start,
    consume_thread_pool_start,
    produce_scheduler_start_shutdown,
    consume_thread_pool_start_shutdown,
)

from starlette.applications import Starlette
from starlette.responses import JSONResponse
from starlette.routing import Route
from starlette.routing import Mount
from mcp.server.fastmcp import FastMCP
from json.decoder import JSONDecodeError

from util.runtime.content_tools import SearchRepoTool, SearchEntityTool
from util.runtime.structure_tools import ExploreTreeStructure_simple

mcp_agent = FastMCP("Agent")

mcp_tools = FastMCP("Tools")


async def handle_update_request(request):
    """
    更新索引API端点
    """
    try:
        data = await request.json()
        return JSONResponse(process_update_request(data))
    except JSONDecodeError as e:
        return JSONResponse(
            {"status": "error", "message": f"请求必须是JSON格式: {str(e)}"},
            status_code=500,
        )
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)


async def handle_delete_index_request(request):
    """
    删除索引API端点
    """
    try:
        data = await request.json()
        return JSONResponse(process_delete_index_request(data))
    except JSONDecodeError as e:
        return JSONResponse(
            {"status": "error", "message": f"请求必须是JSON格式: {str(e)}"},
            status_code=500,
        )
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)


async def handle_get_config_request(request):
    """
    获取白名单和支持的文件类型API端点
    """
    try:
        data = await request.json()
        return JSONResponse(process_get_config_request(data))
    except JSONDecodeError as e:
        return JSONResponse(
            {"status": "error", "message": f"请求必须是JSON格式: {str(e)}"},
            status_code=500,
        )
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)


async def handle_locagent_request(request):
    try:
        data = await request.json()
        required_fields = {"user_name", "root_path", "problem_statement"}
        missing_fields = [field for field in required_fields if field not in data]

        if missing_fields:
            return JSONResponse(
                {
                    "status": "error",
                    "message": f"Missing necessary fields: {', '.join(missing_fields)}",
                },
                400,
            )
        user_name = data["user_name"]
        root_path = data["root_path"]
        problem_statement = data["problem_statement"]

        return JSONResponse(retrieve_code(problem_statement, user_name, root_path))
    except JSONDecodeError as e:
        return JSONResponse(
            {"status": "error", "message": f"请求必须是JSON格式: {str(e)}"},
            status_code=500,
        )
    except Exception as e:
        return JSONResponse({"status": "error", "message": str(e)}, status_code=500)


@mcp_agent.tool(
    name="retrieve_code",
    description="根据用户指令，获取完成指令所需的相关代码上下文，返回代码路径和代码行号。",
)
def retrieve_code(user_instruction: str, user_name: str, workspace: str) -> str:
    try:
        pred = _retrieve_code(user_instruction, user_name, workspace)
        return {"reasoning": pred.reasoning, "locations": pred.locations}, 200
    except Exception as e:
        return {"status": "error", "message": "Index not found", "error": str(e)}, 400


@mcp_tools.tool(
    name=SearchRepoTool["function"]["name"],
    description=SearchRepoTool["function"]["description"],
)
def search_code_snippets(
    user_name: str,
    workspace: str,
    search_terms: list[str] | None = None,
    line_nums: list | None = None,
    file_path_or_pattern: str | None = "**/*.py",
) -> str:
    try:
        repo_ops = load_repo_ops(user_name, workspace)
        return repo_ops.search_code_snippets(
            search_terms=search_terms,
            line_nums=line_nums,
            file_path_or_pattern=file_path_or_pattern,
        )
    except Exception as e:
        return {"status": "error", "message": "Index not found", "error": str(e)}

@mcp_tools.tool(
    name=SearchEntityTool["function"]["name"],
    description=SearchEntityTool["function"]["description"],
)
def get_entity_contents(user_name: str, workspace: str, entity_names: list[str]) -> str:
    try:
        repo_ops = load_repo_ops(user_name, workspace)
        return repo_ops.get_entity_contents(entity_names=entity_names)
    except Exception as e:
        return {"status": "error", "message": "Index not found", "error": str(e)}

@mcp_tools.tool(
    name=ExploreTreeStructure_simple["function"]["name"],
    description=ExploreTreeStructure_simple["function"]["description"],
)
def explore_tree_structure(
    user_name: str,
    workspace: str,
    start_entities: list[str],
    direction: str = "downstream",
    traversal_depth: int = 2,
    entity_type_filter: list[str] | None = None,
    dependency_type_filter: list[str] | None = None,
) -> str:
    try:
        repo_ops = load_repo_ops(user_name, workspace)
        return repo_ops.explore_tree_structure(
            start_entities=start_entities,
            direction=direction,
            traversal_depth=traversal_depth,
            entity_type_filter=entity_type_filter,
            dependency_type_filter=dependency_type_filter,
        )
    except Exception as e:
        return {"status": "error", "message": "Index not found", "error": str(e)}


app = Starlette(
    debug=True,
    routes=[
        Route(
            "/api/CodeBaseIndex/indexSingleFile",
            handle_update_request,
            methods=["POST"],
        ),
        Route(
            "/api/CodeBaseIndex/deleteAllIndex",
            handle_delete_index_request,
            methods=["POST"],
        ),
        Route(
            "/api/CodeBaseIndex/getConfig", handle_get_config_request, methods=["POST"]
        ),
        Route("/api/LocAgent", handle_locagent_request, methods=["POST"]),
        Mount("/mcp/agent", app=mcp_agent.sse_app()),
        Mount("/mcp/tools", app=mcp_tools.sse_app()),
    ],
)


@app.on_event("startup")
def startup_event():
    file_sync_thread_start()
    produce_scheduler_start()
    consume_thread_pool_start()


@app.on_event("shutdown")
def shutdown_event():
    file_sync_thread_shutdown()
    produce_scheduler_start_shutdown()
    consume_thread_pool_start_shutdown()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=5000)
