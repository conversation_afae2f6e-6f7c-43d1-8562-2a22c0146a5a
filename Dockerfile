# docker build . --tag zxccds-release-docker.artnj.zte.com.cn/swap/locagent_remote:20250812_01
FROM zxccds-release-docker.artnj.zte.com.cn/swap/locagent:20250724

COPY . /LocAgent
WORKDIR /LocAgent
# RUN https_proxy=http://proxyhk.zte.com.cn:80 pip install --progress-bar off --no-cache -r requirements.txt
RUN pip install --progress-bar off --no-cache -r requirements.txt --index-url https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple --trusted-host mirrors.zte.com.cn